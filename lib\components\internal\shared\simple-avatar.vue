<template>
  <div
    class="rounded-circle"
    :style="{
      position: 'relative',
      display: 'inline-block'
    }"
  >
    <v-avatar v-bind="{ ...defaultAttrs, ...$attrs }" v-on="{ ...$listeners }">
      <!-- Use v-img directly without CDN transformation -->
      <v-img
        v-if="show === 'img'"
        :src="src"
        :height="size.toString()"
        @error="onImageError"
      />

      <!-- Show initials as fallback -->
      <span
        v-else-if="name"
        class="font-weight-bold grey--text text--lighten-5"
        v-bind="{ ...defaultAttrs, ...$attrs }"
        >{{ initials }}</span
      >

      <!-- Show icon as final fallback -->
      <v-icon v-else :size="sizeInput * 2">{{ icon }}</v-icon>

      <v-overlay v-show="shouldShowLoading" absolute>
        <v-progress-circular
          color="primary"
          size="20"
          indeterminate
        ></v-progress-circular>
      </v-overlay>
    </v-avatar>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  watch
} from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'SimpleAvatarComponent',
  props: {
    name: {
      type: String,
      default: null
    },
    userId: {
      type: String,
      default: null
    },
    src: {
      type: String,
      default: null
    },
    icon: {
      type: String,
      default: '$account'
    },
    size: {
      type: [String, Number],
      default: 50
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  setup(props, { emit }) {
    const user = reactive({
      id: ''
    })
    const uploading = ref(false)

    const colors = [
      '#1ABC9C',
      '#3498DB',
      '#9B59B6',
      '#34495E',
      '#F1C40F',
      '#E74C3C',
      '#277634',
      '#838DD2',
      '#93711D',
      '#B4A67F',
      '#896446',
      '#A8BF0B',
      '#BBD4F3',
      '#18CCE8',
      '#9D69AB',
      '#76B2D7',
      '#4A1A00',
      '#CAAD64',
      '#811836',
      '#C26732',
      '#42365F'
    ]

    const avatarColor = () => {
      const idString = '0' + (props.userId || '')
      const numberPattern = /\d+/g
      const colorIndex =
        parseInt((idString.match(numberPattern) || []).join('')) % colors.length
      return colors[colorIndex]
    }
    
    const defaultAttrs = computed(() => {
      return {
        ...props,
        color: avatarColor()
      }
    })
    
    const sizeInput = computed(() => (props.size as number) / 2.5)

    const show = ref('name')

    // Watch for icon prop
    watch(
      () => props?.icon,
      (val) => {
        if (val) {
          show.value = 'icon'
        }
      },
      {
        immediate: true
      }
    )

    // Watch for src prop
    watch(
      () => props?.src,
      (val) => {
        if (val) {
          show.value = 'img'
        }
      },
      {
        immediate: true
      }
    )

    const onImageError = (error: any) => {
      console.error('Simple avatar image error:', error)
      console.log('Image src:', props.src)
      console.log('Falling back to initials/icon...')
      
      if (props.icon) show.value = 'icon'
      else show.value = 'name'
      
      emit('error', error)
    }

    const initials = computed(() =>
      props.name
        ? props.name
            .split(' ')
            .filter((_i, index, arr) => index === 0 || index === arr.length - 1)
            .map((i) => i.substring(0, 1))
            .join('')
            .toUpperCase()
        : 'null'
    )

    const shouldShowLoading = computed(() => props.loading || uploading.value)

    return {
      defaultAttrs,
      show,
      onImageError,
      initials,
      shouldShowLoading,
      sizeInput
    }
  }
})
</script>

<style scoped>
.v-avatar {
  background-color: transparent !important;
}
</style>
