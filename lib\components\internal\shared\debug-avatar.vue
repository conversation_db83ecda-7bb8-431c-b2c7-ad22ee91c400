<template>
  <div
    class="rounded-circle"
    :style="{
      position: 'relative',
      display: 'inline-block'
    }"
  >
    <v-avatar v-bind="{ ...defaultAttrs, ...$attrs }" v-on="{ ...$listeners }">
      <!-- Try direct image first -->
      <v-img
        v-if="show === 'direct'"
        :src="src"
        :height="size.toString()"
        @error="onDirectImageError"
      />
      
      <!-- Fallback to w-img -->
      <w-img
        v-else-if="show === 'w-img'"
        :src="src"
        :height="size.toString()"
        @error="onWImageError"
      />

      <!-- Show initials as fallback -->
      <span
        v-else-if="name"
        class="font-weight-bold grey--text text--lighten-5"
        v-bind="{ ...defaultAttrs, ...$attrs }"
        >{{ initials }}</span
      >

      <!-- Show icon as final fallback -->
      <v-icon v-else :size="sizeInput * 2">{{ icon }}</v-icon>

      <v-overlay v-show="shouldShowLoading" absolute>
        <v-progress-circular
          color="primary"
          size="20"
          indeterminate
        ></v-progress-circular>
      </v-overlay>
    </v-avatar>

    <!-- Debug info -->
    <div v-if="showDebugInfo" style="position: absolute; top: 100%; left: 0; font-size: 10px; color: red; white-space: nowrap; z-index: 1000;">
      <div>Show: {{ show }}</div>
      <div>Src: {{ src }}</div>
      <div>Name: {{ name }}</div>
      <div>Errors: {{ errorCount }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref
} from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'DebugAvatarComponent',
  props: {
    name: {
      type: String,
      default: null
    },
    userId: {
      type: String,
      default: null
    },
    src: {
      type: String,
      default: null
    },
    icon: {
      type: String,
      default: '$account'
    },
    size: {
      type: [String, Number],
      default: 50
    },
    loading: {
      type: Boolean,
      default: false
    },
    showDebugInfo: {
      type: Boolean,
      default: true
    }
  },

  setup(props, { emit }) {
    const user = reactive({
      id: ''
    })
    const uploading = ref(false)
    const errorCount = ref(0)

    const colors = [
      '#1ABC9C',
      '#3498DB',
      '#9B59B6',
      '#34495E',
      '#F1C40F',
      '#E74C3C',
      '#277634',
      '#838DD2',
      '#93711D',
      '#B4A67F',
      '#896446',
      '#A8BF0B',
      '#BBD4F3',
      '#18CCE8',
      '#9D69AB',
      '#76B2D7',
      '#4A1A00',
      '#CAAD64',
      '#811836',
      '#C26732',
      '#42365F'
    ]

    const avatarColor = () => {
      const idString = '0' + (props.userId || '')
      const numberPattern = /\d+/g
      const colorIndex =
        parseInt((idString.match(numberPattern) || []).join('')) % colors.length
      return colors[colorIndex]
    }
    
    const defaultAttrs = computed(() => {
      return {
        ...props,
        color: avatarColor()
      }
    })
    
    const sizeInput = computed(() => (props.size as number) / 2.5)

    const show = ref('direct') // Start with direct image

    const onDirectImageError = (error: any) => {
      console.error('Direct image error:', error)
      console.log('Trying w-img fallback...')
      errorCount.value++
      show.value = 'w-img'
    }

    const onWImageError = (error: any) => {
      console.error('W-img error:', error)
      console.log('Falling back to initials...')
      errorCount.value++
      if (props.icon) show.value = 'icon'
      else show.value = 'name'
    }

    const initials = computed(() =>
      props.name
        ? props.name
            .split(' ')
            .filter((_i, index, arr) => index === 0 || index === arr.length - 1)
            .map((i) => i.substring(0, 1))
            .join('')
            .toUpperCase()
        : 'null'
    )

    const shouldShowLoading = computed(() => props.loading || uploading.value)

    return {
      defaultAttrs,
      show,
      onDirectImageError,
      onWImageError,
      initials,
      shouldShowLoading,
      sizeInput,
      errorCount
    }
  }
})
</script>

<style scoped>
.v-avatar {
  background-color: transparent !important;
}
</style>
